<?php

namespace Tests\Feature;

use App\Models\O2oErrandOrder;
use App\Models\Rider;
use App\Models\User;
use App\Services\HaiboService;
use App\Services\RiderLocationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Redis;
use Tests\TestCase;

class HaiboRiderLocationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $haiboService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->haiboService = new HaiboService();
    }

    /**
     * 测试骑手位置查询 - 订单不存在的情况
     */
    public function test_rider_location_order_not_found()
    {
        $data = [
            'orderId' => 'HB_TEST_ORDER_NOT_EXISTS'
        ];

        $result = $this->haiboService->getRiderLocation($data);

        $this->assertEquals(HaiboService::RESULT_PARAM_ERROR, $result['code']);
        $this->assertEquals('订单不存在', $result['message']);
        $this->assertNull($result['data']);
    }

    /**
     * 测试骑手位置查询 - 订单存在但无骑手
     */
    public function test_rider_location_order_exists_no_rider()
    {
        // 创建测试用户
        $user = User::factory()->create();
        
        // 创建测试订单（无骑手）
        $order = O2oErrandOrder::create([
            'out_order_no' => 'HB_TEST_ORDER_NO_RIDER',
            'app_key' => O2oErrandOrder::APP_KEY_HB,
            'user_id' => $user->id,
            'order_status' => O2oErrandOrder::STATUS_PAID,
            'type' => O2oErrandOrder::TYPE_SEND,
            'order_amount' => 1000,
            'actual_amount' => 1000,
            'pickup_name' => '测试发件人',
            'pickup_phone' => '13800138000',
            'pickup_address' => '测试发件地址',
            'pickup_lng' => 120.123456,
            'pickup_lat' => 30.123456,
            'deliver_name' => '测试收件人',
            'deliver_phone' => '13800138001',
            'deliver_address' => '测试收件地址',
            'deliver_lng' => 120.234567,
            'deliver_lat' => 30.234567,
            'goods_desc' => '测试商品',
            'distance' => 5000,
            'create_time' => now(),
        ]);

        $data = [
            'orderId' => 'HB_TEST_ORDER_NO_RIDER'
        ];

        $result = $this->haiboService->getRiderLocation($data);

        $this->assertEquals(HaiboService::RESULT_SUCCESS, $result['code']);
        $this->assertEquals('成功', $result['message']);
        $this->assertIsArray($result['data']);
        $this->assertEquals('HB_TEST_ORDER_NO_RIDER', $result['data']['orderId']);
        $this->assertEquals($order->order_no, $result['data']['carrierDeliveryId']);
        $this->assertEquals(0, $result['data']['riderLng']);
        $this->assertEquals(0, $result['data']['riderLat']);
    }

    /**
     * 测试骑手位置查询 - 订单存在且有骑手位置
     */
    public function test_rider_location_order_exists_with_rider()
    {
        // 创建测试用户
        $user = User::factory()->create();
        
        // 创建测试骑手
        $rider = Rider::factory()->create();
        
        // 模拟骑手位置（经度120.123456，纬度30.123456）
        $testLng = 120.123456;
        $testLat = 30.123456;
        
        // 将骑手位置存入Redis
        Redis::geoadd('location_2', $testLng, $testLat, $rider->id);
        
        // 创建测试订单（有骑手）
        $order = O2oErrandOrder::create([
            'out_order_no' => 'HB_TEST_ORDER_WITH_RIDER',
            'app_key' => O2oErrandOrder::APP_KEY_HB,
            'user_id' => $user->id,
            'rider_id' => $rider->id,
            'order_status' => O2oErrandOrder::STATUS_DELIVERY, // 配送中
            'type' => O2oErrandOrder::TYPE_SEND,
            'order_amount' => 1000,
            'actual_amount' => 1000,
            'pickup_name' => '测试发件人',
            'pickup_phone' => '13800138000',
            'pickup_address' => '测试发件地址',
            'pickup_lng' => 120.123456,
            'pickup_lat' => 30.123456,
            'deliver_name' => '测试收件人',
            'deliver_phone' => '13800138001',
            'deliver_address' => '测试收件地址',
            'deliver_lng' => 120.234567,
            'deliver_lat' => 30.234567,
            'goods_desc' => '测试商品',
            'distance' => 5000,
            'create_time' => now(),
        ]);

        $data = [
            'orderId' => 'HB_TEST_ORDER_WITH_RIDER'
        ];

        $result = $this->haiboService->getRiderLocation($data);

        $this->assertEquals(HaiboService::RESULT_SUCCESS, $result['code']);
        $this->assertEquals('成功', $result['message']);
        $this->assertIsArray($result['data']);
        $this->assertEquals('HB_TEST_ORDER_WITH_RIDER', $result['data']['orderId']);
        $this->assertEquals($order->order_no, $result['data']['carrierDeliveryId']);
        
        // 验证坐标转换（乘以10^6）
        $expectedLng = intval($testLng * 1000000);
        $expectedLat = intval($testLat * 1000000);
        $this->assertEquals($expectedLng, $result['data']['riderLng']);
        $this->assertEquals($expectedLat, $result['data']['riderLat']);
    }

    /**
     * 测试骑手位置查询 - 订单已完成
     */
    public function test_rider_location_order_finished()
    {
        // 创建测试用户
        $user = User::factory()->create();
        
        // 创建测试骑手
        $rider = Rider::factory()->create();
        
        // 创建已完成的测试订单
        $order = O2oErrandOrder::create([
            'out_order_no' => 'HB_TEST_ORDER_FINISHED',
            'app_key' => O2oErrandOrder::APP_KEY_HB,
            'user_id' => $user->id,
            'rider_id' => $rider->id,
            'order_status' => O2oErrandOrder::STATUS_FINISH, // 已完成
            'type' => O2oErrandOrder::TYPE_SEND,
            'order_amount' => 1000,
            'actual_amount' => 1000,
            'pickup_name' => '测试发件人',
            'pickup_phone' => '13800138000',
            'pickup_address' => '测试发件地址',
            'pickup_lng' => 120.123456,
            'pickup_lat' => 30.123456,
            'deliver_name' => '测试收件人',
            'deliver_phone' => '13800138001',
            'deliver_address' => '测试收件地址',
            'deliver_lng' => 120.234567,
            'deliver_lat' => 30.234567,
            'goods_desc' => '测试商品',
            'distance' => 5000,
            'create_time' => now(),
            'finish_time' => now(),
        ]);

        $data = [
            'orderId' => 'HB_TEST_ORDER_FINISHED'
        ];

        $result = $this->haiboService->getRiderLocation($data);

        $this->assertEquals(HaiboService::RESULT_SUCCESS, $result['code']);
        $this->assertEquals('成功', $result['message']);
        $this->assertIsArray($result['data']);
        $this->assertEquals('HB_TEST_ORDER_FINISHED', $result['data']['orderId']);
        $this->assertEquals($order->order_no, $result['data']['carrierDeliveryId']);
        // 已完成的订单不返回位置信息
        $this->assertEquals(0, $result['data']['riderLng']);
        $this->assertEquals(0, $result['data']['riderLat']);
    }

    protected function tearDown(): void
    {
        // 清理Redis测试数据
        Redis::flushdb();
        parent::tearDown();
    }
}
