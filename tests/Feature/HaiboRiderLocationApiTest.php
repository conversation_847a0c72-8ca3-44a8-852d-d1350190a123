<?php

namespace Tests\Feature;

use App\Models\O2oErrandOrder;
use App\Models\Rider;
use App\Models\User;
use App\Services\HaiboService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Redis;
use Tests\TestCase;

class HaiboRiderLocationApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * 测试骑手位置查询API - 参数验证失败
     */
    public function test_rider_location_api_validation_fails()
    {
        $response = $this->postJson('/api/haibo/rider-location', []);

        $response->assertStatus(200)
            ->assertJson([
                'code' => HaiboService::RESULT_PARAM_ERROR,
                'message' => '参数验证失败: 海博平台订单号不能为空',
                'data' => null
            ]);
    }

    /**
     * 测试骑手位置查询API - 订单号长度不符合要求
     */
    public function test_rider_location_api_order_id_length_invalid()
    {
        $response = $this->postJson('/api/haibo/rider-location', [
            'orderId' => 'SHORT' // 长度不足18个字符
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'code' => HaiboService::RESULT_PARAM_ERROR,
                'message' => '参数验证失败: 订单号长度不能少于18个字符',
                'data' => null
            ]);
    }

    /**
     * 测试骑手位置查询API - 订单不存在
     */
    public function test_rider_location_api_order_not_found()
    {
        $response = $this->postJson('/api/haibo/rider-location', [
            'orderId' => 'HB_TEST_ORDER_NOT_EXISTS_123456789'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'code' => HaiboService::RESULT_PARAM_ERROR,
                'message' => '订单不存在',
                'data' => null
            ]);
    }

    /**
     * 测试骑手位置查询API - 成功获取骑手位置
     */
    public function test_rider_location_api_success_with_rider()
    {
        // 创建测试用户
        $user = User::factory()->create();

        // 创建测试骑手
        $rider = Rider::factory()->create();

        // 模拟骑手位置
        $testLng = 120.123456;
        $testLat = 30.123456;
        Redis::geoadd('location_2', $testLng, $testLat, $rider->id);

        // 创建测试订单
        $order = O2oErrandOrder::create([
            'out_order_no' => 'HB_TEST_API_ORDER_WITH_RIDER_123456789',
            'app_key' => O2oErrandOrder::APP_KEY_HB,
            'user_id' => $user->id,
            'rider_id' => $rider->id,
            'order_status' => O2oErrandOrder::STATUS_DELIVERY,
            'type' => O2oErrandOrder::TYPE_SEND,
            'order_amount' => 1000,
            'actual_amount' => 1000,
            'pickup_name' => '测试发件人',
            'pickup_phone' => '13800138000',
            'pickup_address' => '测试发件地址',
            'pickup_lng' => 120.123456,
            'pickup_lat' => 30.123456,
            'deliver_name' => '测试收件人',
            'deliver_phone' => '13800138001',
            'deliver_address' => '测试收件地址',
            'deliver_lng' => 120.234567,
            'deliver_lat' => 30.234567,
            'goods_desc' => '测试商品',
            'distance' => 5000,
            'create_time' => now(),
        ]);

        $response = $this->postJson('/api/haibo/rider-location', [
            'orderId' => 'HB_TEST_API_ORDER_WITH_RIDER_123456789'
        ]);

        $expectedLng = intval($testLng * 1000000);
        $expectedLat = intval($testLat * 1000000);

        $response->assertStatus(200)
            ->assertJson([
                'code' => HaiboService::RESULT_SUCCESS,
                'message' => '成功',
                'data' => [
                    'orderId' => 'HB_TEST_API_ORDER_WITH_RIDER_123456789',
                    'carrierDeliveryId' => $order->order_no,
                    'riderLng' => $expectedLng,
                    'riderLat' => $expectedLat,
                ]
            ]);
    }

    /**
     * 测试骑手位置查询API - 订单无骑手
     */
    public function test_rider_location_api_success_no_rider()
    {
        // 创建测试用户
        $user = User::factory()->create();

        // 创建测试订单（无骑手）
        $order = O2oErrandOrder::create([
            'out_order_no' => 'HB_TEST_API_ORDER_NO_RIDER_123456789',
            'app_key' => O2oErrandOrder::APP_KEY_HB,
            'user_id' => $user->id,
            'order_status' => O2oErrandOrder::STATUS_PAID,
            'type' => O2oErrandOrder::TYPE_SEND,
            'order_amount' => 1000,
            'actual_amount' => 1000,
            'pickup_name' => '测试发件人',
            'pickup_phone' => '13800138000',
            'pickup_address' => '测试发件地址',
            'pickup_lng' => 120.123456,
            'pickup_lat' => 30.123456,
            'deliver_name' => '测试收件人',
            'deliver_phone' => '13800138001',
            'deliver_address' => '测试收件地址',
            'deliver_lng' => 120.234567,
            'deliver_lat' => 30.234567,
            'goods_desc' => '测试商品',
            'distance' => 5000,
            'create_time' => now(),
        ]);

        $response = $this->postJson('/api/haibo/rider-location', [
            'orderId' => 'HB_TEST_API_ORDER_NO_RIDER_123456789'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'code' => HaiboService::RESULT_SUCCESS,
                'message' => '成功',
                'data' => [
                    'orderId' => 'HB_TEST_API_ORDER_NO_RIDER_123456789',
                    'carrierDeliveryId' => $order->order_no,
                    'riderLng' => 0,
                    'riderLat' => 0,
                ]
            ]);
    }

    protected function tearDown(): void
    {
        // 清理Redis测试数据
        Redis::flushdb();
        parent::tearDown();
    }
}
