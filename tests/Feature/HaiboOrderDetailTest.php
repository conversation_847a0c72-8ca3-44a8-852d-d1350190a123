<?php

namespace Tests\Feature;

use App\Models\O2oErrandOrder;
use App\Models\User;
use App\Models\Merchant;
use App\Services\HaiboService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class HaiboOrderDetailTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $haiboService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->haiboService = new HaiboService();
    }

    /**
     * 测试订单详情接口 - 订单存在的情况
     */
    public function test_order_detail_success()
    {
        // 创建测试用户
        $user = User::factory()->create();
        
        // 创建测试商家
        $merchant = Merchant::factory()->create([
            'user_id' => $user->id,
            'merchant_type' => 'haibo'
        ]);

        // 创建测试订单
        $order = O2oErrandOrder::factory()->create([
            'out_order_no' => 'HB_TEST_ORDER_123456789',
            'app_key' => O2oErrandOrder::APP_KEY_HB,
            'user_id' => $user->id,
            'order_status' => O2oErrandOrder::STATUS_PAID,
            'actual_amount' => 1000, // 10元
            'gratuity' => 200, // 2元小费
            'goods_protected_price' => 100, // 1元保价费
            'coupon_amount' => 50, // 0.5元优惠
            'distance' => 2500, // 2.5公里
            'create_time' => Carbon::now(),
            'estimated_delivery_time' => Carbon::now()->addHour(),
        ]);

        // 调用订单详情接口
        $result = $this->haiboService->getOrderDetail([
            'orderId' => 'HB_TEST_ORDER_123456789'
        ]);

        // 验证返回结果
        $this->assertEquals(HaiboService::RESULT_SUCCESS, $result['code']);
        $this->assertEquals('成功', $result['message']);
        $this->assertNotNull($result['data']);

        $orderDetail = $result['data'];
        
        // 验证基础字段
        $this->assertEquals('HB_TEST_ORDER_123456789', $orderDetail['orderId']);
        $this->assertEquals($order->order_no, $orderDetail['carrierDeliveryId']);
        $this->assertEquals(HaiboService::HB_STATUS_CREATED, $orderDetail['status']);
        $this->assertEquals($order->updated_at->timestamp, $orderDetail['operateTime']);
        $this->assertEquals($order->create_time->timestamp, $orderDetail['createOrderTime']);
        
        // 验证费用字段
        $this->assertEquals(13.0, $orderDetail['actualFee']); // (1000+200+100)/100 = 13.0
        $this->assertEquals(10.0, $orderDetail['deliveryFee']); // 1000/100 = 10.0
        $this->assertEquals(0.5, $orderDetail['discountFee']); // 50/100 = 0.5
        $this->assertEquals(1.0, $orderDetail['insuredFee']); // 100/100 = 1.0
        $this->assertEquals(2500, $orderDetail['deliveryDistance']);
        
        // 验证可选字段
        $this->assertArrayHasKey('predictDeliveryTime', $orderDetail);
        $this->assertArrayHasKey('tipFee', $orderDetail);
        $this->assertEquals(2.0, $orderDetail['tipFee']); // 200/100 = 2.0
    }

    /**
     * 测试订单详情接口 - 订单不存在的情况
     */
    public function test_order_detail_not_found()
    {
        $result = $this->haiboService->getOrderDetail([
            'orderId' => 'NON_EXISTENT_ORDER'
        ]);

        $this->assertEquals(HaiboService::RESULT_PARAM_ERROR, $result['code']);
        $this->assertEquals('订单不存在', $result['message']);
        $this->assertNull($result['data']);
    }

    /**
     * 测试订单详情接口 - 已取消订单
     */
    public function test_order_detail_cancelled_order()
    {
        // 创建测试用户
        $user = User::factory()->create();
        
        // 创建已取消的测试订单
        $order = O2oErrandOrder::factory()->create([
            'out_order_no' => 'HB_CANCELLED_ORDER_123',
            'app_key' => O2oErrandOrder::APP_KEY_HB,
            'user_id' => $user->id,
            'order_status' => O2oErrandOrder::STATUS_CANCEL,
            'actual_amount' => 1000,
            'refund_amount' => 800, // 退款8元，扣费2元
            'close_reason' => '商户取消订单',
        ]);

        $result = $this->haiboService->getOrderDetail([
            'orderId' => 'HB_CANCELLED_ORDER_123'
        ]);

        $this->assertEquals(HaiboService::RESULT_SUCCESS, $result['code']);
        
        $orderDetail = $result['data'];
        $this->assertEquals(HaiboService::HB_STATUS_CANCELLED, $orderDetail['status']);
        $this->assertArrayHasKey('cancelReasonCode', $orderDetail);
        $this->assertArrayHasKey('cancelReasonDesc', $orderDetail);
        $this->assertArrayHasKey('cancelFee', $orderDetail);
        $this->assertEquals('商户取消订单', $orderDetail['cancelReasonDesc']);
        $this->assertEquals(2.0, $orderDetail['cancelFee']); // (1000-800)/100 = 2.0
    }

    /**
     * 测试HTTP接口
     */
    public function test_order_detail_http_endpoint()
    {
        // 创建测试订单
        $user = User::factory()->create();
        $order = O2oErrandOrder::factory()->create([
            'out_order_no' => 'HB_HTTP_TEST_ORDER_456',
            'app_key' => O2oErrandOrder::APP_KEY_HB,
            'user_id' => $user->id,
            'order_status' => O2oErrandOrder::STATUS_DELIVERY,
        ]);

        // 发送HTTP请求
        $response = $this->postJson('/api/haibo/order-detail', [
            'orderId' => 'HB_HTTP_TEST_ORDER_456'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'code' => HaiboService::RESULT_SUCCESS,
                    'message' => '成功'
                ])
                ->assertJsonStructure([
                    'code',
                    'message',
                    'data' => [
                        'orderId',
                        'carrierDeliveryId',
                        'status',
                        'operateTime',
                        'createOrderTime',
                        'actualFee',
                        'deliveryFee',
                        'deliveryDistance'
                    ]
                ]);
    }

    /**
     * 测试HTTP接口参数验证
     */
    public function test_order_detail_validation()
    {
        // 测试缺少orderId参数
        $response = $this->postJson('/api/haibo/order-detail', []);
        
        $response->assertStatus(200)
                ->assertJson([
                    'code' => HaiboService::RESULT_PARAM_ERROR
                ]);

        // 测试orderId长度不符合要求
        $response = $this->postJson('/api/haibo/order-detail', [
            'orderId' => 'short'
        ]);
        
        $response->assertStatus(200)
                ->assertJson([
                    'code' => HaiboService::RESULT_PARAM_ERROR
                ]);
    }
}
